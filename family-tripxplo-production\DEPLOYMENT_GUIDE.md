# Database Authentication Fix - Deployment Guide

## Issue Fixed
The production server was using corrupted JWT tokens in the environment configuration, causing database queries to fail and fall back to sample data instead of retrieving real package data.

## Files Changed
1. `api/.env.production` - Fixed corrupted JWT tokens
2. `api/server.js` - Added database connection test endpoint

## Manual Deployment Steps

### 1. Upload Fixed Files to Production Server

```bash
# Upload API directory with fixed environment configuration
scp -r family-tripxplo-production/api root@*************:/var/www/family/

# Upload JavaScript files (if needed)
scp -r family-tripxplo-production/js root@*************:/var/www/family/
```

### 2. Configure Production Environment

```bash
# SSH into production server
ssh root@*************

# Navigate to API directory
cd /var/www/family/api

# Backup existing .env if it exists
cp .env .env.backup.$(date +%Y%m%d-%H%M%S)

# Copy the production environment file to .env
cp .env.production .env

# Install/update dependencies
npm install
```

### 3. Restart API Service

```bash
# If using PM2
pm2 restart family-api || pm2 start server.js --name family-api

# If using systemctl
systemctl restart family-api

# If no process manager, manually restart the Node.js process
```

### 4. Test Database Connections

```bash
# Test database connections via API endpoint
curl -s http://localhost:3000/api/test-db | jq '.'

# Or test from browser
# https://family.tripxplo.com/api/test-db
```

## Automated Deployment

Run the PowerShell deployment script:

```powershell
.\family-tripxplo-production\deploy-fix.ps1
```

## Verification Steps

1. **Test Website**: Visit https://family.tripxplo.com
2. **Check Database Connection**: Visit https://family.tripxplo.com/api/test-db
3. **Search for Packages**: Try searching for "Andaman" or other destinations
4. **Monitor Console**: Check browser console for database connection logs

## Expected Results After Fix

- Database connection test should show all connections as "success"
- Package searches should return real data instead of fallback/sample data
- Console logs should show successful database queries
- No more "creating sample packages" warnings

## Key Changes Made

### Fixed JWT Tokens in .env.production:

**Before (Corrupted):**
```
CRM_ANON_KEY=eyJ...Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
QUOTE_ANON_KEY=eyJ...Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
```

**After (Valid):**
```
CRM_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4NTcwMjgsImV4cCI6MjA2MDQzMzAyOH0.fCaJNbHL6VwKxTbt3vYl2F5O2gRoMFuUO1bhqEtSWpI
QUOTE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE
```

## Troubleshooting

If issues persist after deployment:

1. **Check Environment Variables**: Ensure `.env` file is properly loaded
2. **Verify Network Access**: Ensure server can reach Supabase URLs
3. **Check Logs**: Monitor server logs for detailed error messages
4. **Test Locally**: Verify the same configuration works in local environment

## Contact

If you encounter any issues during deployment, please check the console logs and the database test endpoint for detailed error information.
