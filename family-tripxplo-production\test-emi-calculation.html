<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMI Calculation Test - TripXplo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .package-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .emi-info {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .expected {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 4px solid #ffc107;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .console-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 EMI Calculation Test</h1>
        <p>This page tests the EMI calculation fix for TripXplo packages.</p>
        
        <div class="expected">
            <h3>Expected Results:</h3>
            <ul>
                <li><strong>Package 1 (₹63,199)</strong>: 6 months = ₹10,533/month</li>
                <li><strong>Package 2 (₹54,096)</strong>: 6 months = ₹9,016/month</li>
                <li><strong>Total amounts should match package prices exactly</strong></li>
            </ul>
        </div>

        <button onclick="testEMICalculation()">🔍 Test Andaman Packages</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="testResults"></div>
        <div id="consoleLog" class="console-log" style="display: none;"></div>
    </div>

    <!-- Include the actual databaseService.js from the production site -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        let consoleLog = [];
        
        // Override console.log to capture logs
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleLog.push(args.join(' '));
            updateConsoleDisplay();
        };

        function updateConsoleDisplay() {
            const logElement = document.getElementById('consoleLog');
            logElement.innerHTML = consoleLog.slice(-50).join('<br>'); // Show last 50 logs
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showConsoleLog() {
            const logElement = document.getElementById('consoleLog');
            logElement.style.display = logElement.style.display === 'none' ? 'block' : 'none';
        }

        async function testEMICalculation() {
            const resultsEl = document.getElementById('testResults');
            resultsEl.innerHTML = '<p>🔄 Testing EMI calculations...</p>';
            consoleLog = []; // Clear previous logs
            
            try {
                // Test search for Andaman packages
                const searchParams = {
                    destination: 'Andaman',
                    travel_date: '2025-12-01',
                    adults: 2,
                    children: 2,
                    child: 0,
                    infants: 0
                };

                console.log('🔍 Testing EMI calculation with params:', searchParams);
                
                const result = await databaseService.searchPackages(searchParams);
                
                if (result.success && result.packages && result.packages.length > 0) {
                    resultsEl.innerHTML = '<h3>✅ Test Results:</h3>';
                    
                    result.packages.forEach((pkg, index) => {
                        const packageDiv = document.createElement('div');
                        packageDiv.className = 'package-card';
                        
                        // Check EMI calculations
                        const emiOptions = pkg.emi_options || [];
                        const sixMonthEmi = emiOptions.find(emi => emi.months === 6);
                        
                        let emiStatus = '';
                        if (sixMonthEmi) {
                            const expectedMonthly = Math.round(pkg.total_price / 6);
                            const actualMonthly = sixMonthEmi.monthly_amount;
                            const isCorrect = Math.abs(expectedMonthly - actualMonthly) <= 1; // Allow 1 rupee difference for rounding
                            
                            emiStatus = isCorrect ? 
                                `<span class="success">✅ EMI calculation is CORRECT</span>` :
                                `<span class="error">❌ EMI calculation is WRONG</span>`;
                        }
                        
                        packageDiv.innerHTML = `
                            <h4>📦 Package ${index + 1}: ${pkg.title || pkg.quote_name || 'Unknown'}</h4>
                            <p><strong>Total Price:</strong> ₹${(pkg.total_price || 0).toLocaleString()}</p>
                            <p><strong>Family Type:</strong> ${pkg.family_type || 'Unknown'}</p>
                            
                            <div class="emi-info">
                                <h5>💳 EMI Options:</h5>
                                ${emiOptions.map(emi => {
                                    const expected = Math.round(pkg.total_price / emi.months);
                                    const isCorrect = Math.abs(expected - emi.monthly_amount) <= 1;
                                    const status = isCorrect ? '✅' : '❌';
                                    
                                    return `
                                        <p>${status} ${emi.months} months: ₹${emi.monthly_amount.toLocaleString()}/month 
                                        (Expected: ₹${expected.toLocaleString()}) 
                                        | Total: ₹${emi.total_amount.toLocaleString()}</p>
                                    `;
                                }).join('')}
                                ${emiStatus}
                            </div>
                        `;
                        
                        resultsEl.appendChild(packageDiv);
                    });
                    
                    // Add console log toggle
                    const logToggle = document.createElement('button');
                    logToggle.textContent = '📋 Show/Hide Console Logs';
                    logToggle.onclick = showConsoleLog;
                    resultsEl.appendChild(logToggle);
                    
                } else {
                    resultsEl.innerHTML = '<p class="error">❌ No packages found or search failed</p>';
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                resultsEl.innerHTML = `<p class="error">❌ Test failed: ${error.message}</p>`;
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleLog').innerHTML = '';
            consoleLog = [];
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(testEMICalculation, 1000);
        });
    </script>
</body>
</html>
